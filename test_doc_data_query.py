import os
import shutil
from utils.constant import LLMAgentType
from models.conversation import Conversation
from models.session import Session
from services.conversation_service import ConversationService
from services.tools.data_retriever.document_data_retriever_agent import DocumentDataQueryAgent

pdf_path = "/Users/<USER>/Projects/LayerNext/local_storage/statements/Statement_Jul_2024_v2.pdf"

query = "Extract all payments to air tickets done in 2024 July from the given bank statement."

conversation, session_id = ConversationService().create_conversation_and_session(
    "65ca39df1e4aa539b205d8a9", query, LLMAgentType.AGENT_TYPE_CHAT
)
# Create attachments folder if not exists
if not os.path.exists(f"storage/public/{conversation.chat_id}/attachments"):
    os.makedirs(f"storage/public/{conversation.chat_id}/attachments")
# Copy the pdf to conversation folder (attachments)
shutil.copy(pdf_path, f"storage/public/{conversation.chat_id}/attachments")

doc_retriever = DocumentDataQueryAgent()

doc_retriever.execute_task(conversation, pdf_path.split("/")[-1], [], query, "./")
