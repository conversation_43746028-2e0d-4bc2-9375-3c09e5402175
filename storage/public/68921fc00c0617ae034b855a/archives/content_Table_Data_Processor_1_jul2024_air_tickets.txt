Python code:

```python
import pandas as pd
from metalake import load_data
import os

# Input parameters
data_files = [
    'Statement_Jul_2024_v2_1_table-1.dat', 'Statement_Jul_2024_v2_2_table-1.dat', 'Statement_Jul_2024_v2_3_table-1.dat',
    'Statement_Jul_2024_v2_4_table-1.dat', 'Statement_Jul_2024_v2_5_table-1.dat', 'Statement_Jul_2024_v2_6_table-1.dat',
    'Statement_Jul_2024_v2_7_table-1.dat', 'Statement_Jul_2024_v2_8_table-1.dat', 'Statement_Jul_2024_v2_9_table-1.dat',
    'Statement_Jul_2024_v2_10_table-1.dat', 'Statement_Jul_2024_v2_11_table-1.dat', 'Statement_Jul_2024_v2_12_table-1.dat',
    'Statement_Jul_2024_v2_13_table-1.dat', 'Statement_Jul_2024_v2_14_table-1.dat'
]
chat_id = "689215b30c0617ae034b8535"

all_dfs = []

# 1. Load all files, treating columns as string, add source_table column
for fname in data_files:
    df = load_data(chat_id, fname)
    if df.empty:
        continue
    # Ensure all columns are string
    df = df.astype(str)
    df['source_table'] = fname
    all_dfs.append(df)

# 2. Vertically concatenate
df_full = pd.concat(all_dfs, ignore_index=True) if all_dfs else pd.DataFrame()

print(f"Loaded {len(data_files)} files.")
print(f"Total rows before filtering: {len(df_full)}")

if df_full.empty:
    print("No data loaded from the files. Exiting.")
else:
    # 3. Filter rows:
    # Concatenate all columns into a single string for each row (exclude 'source_table')
    col_names = [col for col in df_full.columns if col != 'source_table']
    concat_text = df_full[col_names].apply(lambda x: ' '.join(x.values.astype(str)), axis=1)
    
    # Condition a: contains 'Jul' (case-insensitive)
    mask_jul = concat_text.str.contains('Jul', case=False, na=False)
    # Condition b: contains any of the airline keywords (case-insensitive)
    airline_keywords = ['AIR', 'AIRCANADA', 'DELTA', 'TICKET']
    pattern_airline = '|'.join(airline_keywords)
    mask_airline = concat_text.str.contains(pattern_airline, case=False, na=False)
    
    # Both conditions
    mask = mask_jul & mask_airline
    df_filtered = df_full[mask].copy()

    print(f"Rows after filtering: {len(df_filtered)}")
    
    # 4. Save filtered rows as CSV
    out_csv = 'files/payments_air_tickets_Jul2024.csv'
    os.makedirs('files', exist_ok=True)
    df_filtered.to_csv(out_csv, index=False)
    
    # 5. Show up to 5 filtered rows for verification
    n_show = min(5, len(df_filtered))
    if n_show == 0:
        print("No matching air-ticket payment transactions for July 2024 found.")
    else:
        print(f"First {n_show} filtered rows:")
        print(df_filtered.head(n_show).to_markdown(index=False))
        if len(df_filtered) > n_show:
            print(f"(Only first {n_show} of {len(df_filtered)} shown)")

````

Output:

Loaded 14 files.
Total rows before filtering: 273
Rows after filtering: 8
First 5 filtered rows:
| 0          | 1                 | 2                        | 3      | 4           | 5   | 6                |      7 |   8 | source_table                         |
|:-----------|:------------------|:-------------------------|:-------|:------------|:----|:-----------------|-------:|----:|:-------------------------------------|
| Jul4 Jul5  | NAIROBI GT        | C HOTEL NAIROBI          | 507.25 | nan         | nan | nan              | nan    | nan | Statement_Jul_2024_v2_6_table-1.dat  |
|            | UNITED STATES     | DOLLAR363.00@ 1.39738    |        |             |     |                  |        |     |                                      |
| Jul7 Jul9  | NAIROBI GT        | C HOTEL NAIROBI          | 561.95 | nan         | nan | nan              | nan    | nan | Statement_Jul_2024_v2_6_table-1.dat  |
|            | UNITED STATES     | DOLLAR401.68@ 1.39900    |        |             |     |                  |        |     |                                      |
| Jun30 Jul1 | AIRCANADA         | ONBOARD CAFE MISSISSAUGA | 27.01  | nan         | nan | nan              | nan    | nan | Statement_Jul_2024_v2_6_table-1.dat  |
| Jul3 Jul3  | DELTA AIR LINE    | S ATLANTA                | N      | L CLASS:    | Q   | IANO/TODD        | 583.4  | nan | Statement_Jul_2024_v2_10_table-1.dat |
|            | UNITED STATES DOL | LAR413.98@ 1.40925       | D      | Q           | ICC |                  |        |     |                                      |
|            | ROUTING: FROM:    | LOS ANGELES INTER        | EN     | GER NAME: P |     |                  |        |     |                                      |
|            | TO: ATLANTA H     | ARTSFIELD CARRIER:       |        |             |     |                  |        |     |                                      |
|            | TO: MOLINE C      | ARRIER: DL CLASS:        |        |             |     |                  |        |     |                                      |
|            | TICKET NUMBER:    | ************** PASS      |        |             |     |                  |        |     |                                      |
| Jul4 Jul6  | AIRCANADA         | WINNIPEG                 | AT     | C CLASS:    | T   | T                | 728.62 | nan | Statement_Jul_2024_v2_10_table-1.dat |
|            | ROUTING: FROM:    | VANCOUVER INTERN         | A      | AC CLASS:   | X   | SON/AMANDA LEANN |        |     |                                      |
|            | TO: WINNIPEG I    | NTERNATI CARRIER:        | AC     | CLASS:      | X   |                  |        |     |                                      |
|            | TO: VANCOUVE      | R INTERNAT CARRIER:      | AC     | CLASS:      | CK  |                  |        |     |                                      |
|            | TO: FICT POINT    | MINUS 7 CARRIER:         | NG     | ER NAME: JA |     |                  |        |     |                                      |
|            | TO: FICT POINT    | MINUS 7 CARRIER:         |        |             |     |                  |        |     |                                      |
|            | TICKET NUMBER:    | ************* PASSE      |        |             |     |                  |        |     |                                      |
(Only first 5 of 8 shown)

