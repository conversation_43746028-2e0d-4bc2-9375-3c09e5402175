Python code:

```python
import pandas as pd
from metalake import load_data
import os

# 1. Load data, treat all columns as string
chat_id = "68921fc00c0617ae034b855a"
data_file = "payments_air_tickets_Jul2024.dat"
df = load_data(chat_id, data_file)

if df.empty:
    print("No data loaded from the file. Exiting.")
else:
    # 2. Concatenate all columns except helper columns (exclude 'source_table' if present) to one lowercase string
    cols_to_concat = [col for col in df.columns if col != 'source_table']
    # Helper: if there are unnamed index columns, ignore them (e.g., if integer columns)
    cols_to_concat = [col for col in cols_to_concat if not (isinstance(col, int) and col < 0)]
    
    df_str = df.copy()
    df_str_all = df_str[cols_to_concat].astype(str).apply(lambda row: ' '.join(row.values).lower(), axis=1)
    
    # 3. At least one airline keyword (case-insensitive, exact or spaced as shown)
    keywords = ["ticket", "aircanada", "air canada", "delta"]
    keep_mask = df_str_all.apply(lambda x: any(kw in x for kw in keywords))
    # 4. Exclude rows with 'hotel' (case-insensitive)
    exclude_mask = df_str_all.str.contains("hotel", case=False, na=False)
    
    # Final filter: keep only if (keep_mask is True and exclude_mask is False)
    df_final = df[keep_mask & ~exclude_mask].copy()
    
    # 5. Save
    out_csv = "files/payments_air_tickets_Jul2024_final.csv"
    os.makedirs('files', exist_ok=True)
    df_final.to_csv(out_csv, index=False)
    
    # 6. Console output
    print(f"Row count before filtering: {len(df)}")
    print(f"Row count after filtering: {len(df_final)}")
    n_show = min(5, len(df_final))
    if n_show == 0:
        print("No airline payment rows remain after filtering.")
    else:
        print(f"First {n_show} rows after filtering:")
        print(df_final.head(n_show).to_markdown(index=False))
        if len(df_final) > n_show:
            print(f"(Only first {n_show} of {len(df_final)} shown)")

````

Output:

Row count before filtering: 8
Row count after filtering: 6
First 5 rows after filtering:
| 0          | 1                 | 2                        | 3     | 4           | 5   | 6                | 7      | 8   | source_table                         |
|:-----------|:------------------|:-------------------------|:------|:------------|:----|:-----------------|:-------|:----|:-------------------------------------|
| Jun30 Jul1 | AIRCANADA         | ONBOARD CAFE MISSISSAUGA | 27.01 |             |     |                  |        |     | Statement_Jul_2024_v2_6_table-1.dat  |
| Jul3 Jul3  | DELTA AIR LINE    | S ATLANTA                | N     | L CLASS:    | Q   | IANO/TODD        | 583.40 |     | Statement_Jul_2024_v2_10_table-1.dat |
|            | UNITED STATES DOL | LAR413.98@ 1.40925       | D     | Q           | ICC |                  |        |     |                                      |
|            | ROUTING: FROM:    | LOS ANGELES INTER        | EN    | GER NAME: P |     |                  |        |     |                                      |
|            | TO: ATLANTA H     | ARTSFIELD CARRIER:       |       |             |     |                  |        |     |                                      |
|            | TO: MOLINE C      | ARRIER: DL CLASS:        |       |             |     |                  |        |     |                                      |
|            | TICKET NUMBER:    | ************** PASS      |       |             |     |                  |        |     |                                      |
| Jul4 Jul6  | AIRCANADA         | WINNIPEG                 | AT    | C CLASS:    | T   | T                | 728.62 |     | Statement_Jul_2024_v2_10_table-1.dat |
|            | ROUTING: FROM:    | VANCOUVER INTERN         | A     | AC CLASS:   | X   | SON/AMANDA LEANN |        |     |                                      |
|            | TO: WINNIPEG I    | NTERNATI CARRIER:        | AC    | CLASS:      | X   |                  |        |     |                                      |
|            | TO: VANCOUVE      | R INTERNAT CARRIER:      | AC    | CLASS:      | CK  |                  |        |     |                                      |
|            | TO: FICT POINT    | MINUS 7 CARRIER:         | NG    | ER NAME: JA |     |                  |        |     |                                      |
|            | TO: FICT POINT    | MINUS 7 CARRIER:         |       |             |     |                  |        |     |                                      |
|            | TICKET NUMBER:    | ************* PASSE      |       |             |     |                  |        |     |                                      |
| Jul5 Jul7  | AIRCANADA         | WINNIPEG                 | AT    | C CLASS:    | T   | T                | 728.62 |     | Statement_Jul_2024_v2_10_table-1.dat |
|            | ROUTING: FROM:    | VANCOUVER INTERN         | A     | AC CLASS:   | X   | /WOOREE          |        |     |                                      |
|            | TO: WINNIPEG I    | NTERNATI CARRIER:        | AC    | CLASS:      | X   |                  |        |     |                                      |
|            | TO: VANCOUVE      | R INTERNAT CARRIER:      | AC    | CLASS:      | HOI |                  |        |     |                                      |
|            | TO: FICT POINT    | MINUS 7 CARRIER:         | ENG   | ER NAME: C  |     |                  |        |     |                                      |
|            | TO: FICT POINT    | MINUS 7 CARRIER:         |       |             |     |                  |        |     |                                      |
|            | TICKET NUMBER:    | ************* PASS       |       |             |     |                  |        |     |                                      |
| Jul9 Jul10 | AIRCANADA         | WINNIPEG                 | AT    | CLASS:      | T   | T                | 73.50  |     | Statement_Jul_2024_v2_11_table-1.dat |
|            | ROUTING: FROM:    | VANCOUVER INTERN         | AC    | AC CLASS:   | X   | SON/AMANDA LEANN |        |     |                                      |
|            | TO: WINNIPEG I    | NTERNATI CARRIER:        | AC    | CLASS:      | X   |                  |        |     |                                      |
|            | TO: VANCOUVE      | R INTERNAT CARRIER:      | AC    | CLASS:      | CK  |                  |        |     |                                      |
|            | TO: FICT POINT    | MINUS 7 CARRIER:         | ENG   | ER NAME: JA |     |                  |        |     |                                      |
|            | TO: FICT POINT    | MINUS 7 CARRIER:         |       |             |     |                  |        |     |                                      |
|            | TICKET NUMBER:    | ************* PASS       |       |             |     |                  |        |     |                                      |
(Only first 5 of 6 shown)

