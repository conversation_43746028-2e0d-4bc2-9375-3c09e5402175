"""
* Copyright (c) 2025 LayerNext Inc.
* all rights reserved.
* This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.

* @class VisualDataExtractorAgent
* @description Tool responsible for extracting data from documents in visual format.
* <AUTHOR>
"""

import os
import base64
from PIL import Image
from services.tools.base_tool import BaseTool, ToolOutput
from utils.constant import AgentState
from models.conversation import Conversation
from services.shared.base_state_handler_agent import BaseStateHandlerAgent


class VisualDataExtractorAgent(BaseTool, BaseStateHandlerAgent):
    def __init__(self):
        BaseTool.__init__(self)
        model_name = os.getenv("MODEL_VISUAL_REVIEWER", "gpt-4.1")
        BaseStateHandlerAgent.__init__(self, "", model_name, AgentState.VISUAL_EXTRACT)

        # Load system instructions from file
        self.system_instruction = ""
        instructions_file = "instructions/tools/visual_data_extractor/system_instructions.txt"
        if os.path.exists(instructions_file):
            with open(instructions_file, "r", encoding="utf-8") as file:
                self.system_instruction = file.read()
        else:
            # Fallback system instruction if file doesn't exist
            self.system_instruction = """You are a visual data extraction assistant. Your task is to analyze images and extract specific information based on the user's request.

Instructions:
1. Carefully examine all visual elements in the provided image(s)
2. Look for the specific information requested by the user
3. Extract text, numbers, tables, charts, diagrams, or other visual data as requested
4. If the image contains multiple pages (from PDF conversion), analyze each page thoroughly
5. Provide structured and organized output based on what you find
6. If no relevant information is found, clearly state that no matches were found
7. Be thorough and accurate in your extraction
8. Maintain the context and relationships between different visual elements
9. If extracting tabular data, preserve the structure and formatting
10. For charts or graphs, describe the data points, trends, and key insights

Return your findings in a clear, structured format. If you find relevant information, organize it logically and provide context about where it was found in the image(s)."""

    def execute_task(
        self,
        conversation: Conversation,
        data_source_name: str,
        data_file_name_list: list,
        task_instruction: str,
        base_log_path: str = "",
        additional_data: dict = None,
    ):
        """
        Execute visual data extraction task on the provided image or PDF file.

        Args:
            conversation: The conversation object
            data_source_name: Name of the image/PDF file to extract data from
            data_file_name_list: Not used (kept for interface compatibility)
            task_instruction: The extraction instruction/query
            base_log_path: Base log path (optional)
            additional_data: Additional data (optional)

        Returns:
            ToolOutput: Contains extraction results and display output
        """
        try:
            # Get chat logger from conversation
            chat_log = conversation.get_chat_logger()

            # Check if file is provided
            if not data_source_name:
                return ToolOutput(
                    display_output="No image or PDF file provided for visual data extraction.",
                    result_text_list=[("No image or PDF file provided for visual data extraction.", False)],
                )

            # Construct file path - files are stored in storage/public/{chat_id}/
            file_path = f"storage/public/{conversation.chat_id}/{data_source_name}"

            # Check if file exists
            if not os.path.exists(file_path):
                error_msg = f"Cannot proceed with visual data extraction. Missing file: {data_source_name}. Please ensure the file exists."
                chat_log.error(f"VisualDataExtractorAgent: {error_msg}")
                return ToolOutput(display_output=error_msg, result_text_list=[(error_msg, False)])

            # Get file extension
            file_extension = data_source_name.lower().split(".")[-1]
            supported_extensions = ["png", "jpg", "jpeg", "pdf"]

            if file_extension not in supported_extensions:
                error_msg = f"Cannot proceed with visual data extraction. Unsupported file type: {file_extension}. Supported types: {', '.join(supported_extensions)}"
                chat_log.error(f"VisualDataExtractorAgent: {error_msg}")
                return ToolOutput(display_output=error_msg, result_text_list=[(error_msg, False)])

            # Process the file based on its type
            image_paths = []

            if file_extension == "pdf":
                # Convert PDF to images
                image_paths = self._convert_pdf_to_images(file_path, conversation.chat_id, chat_log)
                if not image_paths:
                    error_msg = f"Failed to convert PDF {data_source_name} to images."
                    chat_log.error(f"VisualDataExtractorAgent: {error_msg}")
                    return ToolOutput(display_output=error_msg, result_text_list=[(error_msg, False)])
            else:
                # Single image file
                image_paths = [file_path]

            # Encode images to base64 for LLM
            encoded_images = []
            for img_path in image_paths:
                try:
                    encoded_image = self._encode_image_to_base64(img_path)
                    if encoded_image:
                        encoded_images.append(
                            {"path": img_path, "data": encoded_image, "filename": os.path.basename(img_path)}
                        )
                except Exception as e:
                    chat_log.error(f"Error encoding image {img_path}: {str(e)}")

            if not encoded_images:
                error_msg = f"Failed to process any images from {data_source_name}."
                chat_log.error(f"VisualDataExtractorAgent: {error_msg}")
                return ToolOutput(display_output=error_msg, result_text_list=[(error_msg, False)])

            # Prepare messages for LLM with images
            prompt_messages = []

            # Add task instruction
            prompt_messages.append({"role": "user", "content": task_instruction})

            # Add images
            for img_data in encoded_images:
                image_content = {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": f"Image: {img_data['filename']}"},
                        {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{img_data['data']}"}},
                    ],
                }
                prompt_messages.append(image_content)

            # Invoke LLM to perform visual data extraction
            chat_log.info(
                f"VisualDataExtractorAgent: Performing visual data extraction for: {task_instruction} on file: {data_source_name}"
            )
            extraction_result = self.invoke_llm(
                conversation=conversation,
                system_instruction=self.system_instruction,
                prompt_messages=prompt_messages,
                chat_log=chat_log,
                is_current_session_history_required=False,
                follow_up_history_load_depth=0,
            )

            if not extraction_result:
                return ToolOutput(
                    display_output="Failed to get extraction results from LLM.",
                    result_text_list=[("Failed to get extraction results from LLM.", False)],
                )

            # Prepare display output
            file_info = f"Processed file: {data_source_name}"
            if file_extension == "pdf":
                file_info += f" (converted to {len(image_paths)} image(s))"

            display_output = f"{file_info}\n\nExtraction Results:\n{extraction_result}"

            # Return the extraction results
            return ToolOutput(display_output=display_output, result_text_list=[(extraction_result, False)])

        except Exception as e:
            error_msg = f"Error during visual data extraction: {str(e)}"
            chat_log.error(error_msg) if "chat_log" in locals() else None
            return ToolOutput(display_output=error_msg, result_text_list=[(error_msg, False)])

    def _convert_pdf_to_images(self, pdf_path: str, chat_id: str, chat_log) -> list:
        """
        Convert PDF pages to images using pdf2image library.

        Args:
            pdf_path: Path to the PDF file
            chat_id: Chat ID for creating output directory
            chat_log: Logger instance

        Returns:
            List of image file paths
        """
        try:
            # Try to import pdf2image
            try:
                from pdf2image import convert_from_path
            except ImportError:
                chat_log.error("pdf2image library not found. Please install it: pip install pdf2image")
                return []

            # Create output directory for converted images
            output_dir = f"storage/public/{chat_id}/pdf_images"
            os.makedirs(output_dir, exist_ok=True)

            # Convert PDF to images
            images = convert_from_path(pdf_path, dpi=200, fmt="PNG")

            image_paths = []
            pdf_name = os.path.splitext(os.path.basename(pdf_path))[0]

            for i, image in enumerate(images, 1):
                image_filename = f"{pdf_name}_page_{i}.png"
                image_path = os.path.join(output_dir, image_filename)
                image.save(image_path, "PNG")
                image_paths.append(image_path)
                chat_log.info(f"Converted PDF page {i} to {image_path}")

            return image_paths

        except Exception as e:
            chat_log.error(f"Error converting PDF to images: {str(e)}")
            return []

    def _encode_image_to_base64(self, image_path: str) -> str:
        """
        Encode an image file to base64 string.

        Args:
            image_path: Path to the image file

        Returns:
            Base64 encoded string of the image
        """
        try:
            with open(image_path, "rb") as image_file:
                return base64.b64encode(image_file.read()).decode("utf-8")
        except Exception as e:
            return ""

    def on_agent_invoked(
        self,
        conversation,
        input_data,
        follow_up_history,
        current_session_history,
        chat_log,
        try_count,
        prev_invoked_count=0,
    ):
        pass

    def on_invoke_limit_reached(self, input_data: dict, conversation: Conversation, chat_log, try_count: int):
        pass

    def update_followup_history(self, conversation, chat_log, output_data):
        pass
